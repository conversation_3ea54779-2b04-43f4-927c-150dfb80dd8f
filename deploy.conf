#!/bin/bash
# =============================================================================
# 部署配置文件 - sanyth-xuegong-5-server
# 使用方法: ./deploy.sh -c deploy.conf
# =============================================================================

# =============================================================================
# 服务器连接配置
# =============================================================================

# 目标服务器IP地址
SERVER_HOST="*************"

# SSH用户名
SERVER_USER="root"

# SSH端口
SERVER_PORT="22"

# SSH密码（直接在此设置，避免每次手动输入）
# 注意：明文存储密码存在安全风险，请确保文件权限正确
SERVER_PASSWORD="Sanyth123!"

# =============================================================================
# 路径配置
# =============================================================================

# 本地JAR文件路径（相对于项目根目录）
LOCAL_JAR_PATH="xggl-api/target/xggl-1.0-SNAPSHOT-api.jar"

# 远程应用目录
REMOTE_APP_DIR="/home/<USER>/xuegong-v5"

# 远程JAR文件路径
REMOTE_JAR_PATH="${REMOTE_APP_DIR}/xggl-1.0-SNAPSHOT-api.jar"

# 远程备份目录
REMOTE_BACKUP_DIR="${REMOTE_APP_DIR}/backup"

# =============================================================================
# 超时配置（秒）
# =============================================================================

# 服务停止超时时间
SHUTDOWN_TIMEOUT=30

# 服务启动超时时间
STARTUP_TIMEOUT=60

# 健康检查超时时间
HEALTH_CHECK_TIMEOUT=120

# =============================================================================
# 健康检查配置
# =============================================================================

# HTTP健康检查URL（可选，如果应用提供健康检查接口）
# 例如: http://localhost:8080/actuator/health
HEALTH_CHECK_URL=""

# 应用监听端口（用于端口检查）
APP_PORT="8080"

# =============================================================================
# 日志配置
# =============================================================================

# 日志文件名前缀
LOG_PREFIX="deploy"

# 是否保留历史日志（true/false）
KEEP_HISTORY_LOGS=true

# 保留日志文件数量
MAX_LOG_FILES=10

# =============================================================================
# Maven配置
# =============================================================================

# Maven构建命令
MAVEN_BUILD_COMMAND="mvn clean package -DskipTests"

# 是否跳过测试
SKIP_TESTS=true

# Maven配置文件（可选）
MAVEN_SETTINGS=""

# =============================================================================
# 备份配置
# =============================================================================

# 是否创建备份
CREATE_BACKUP=true

# 备份保留天数
BACKUP_RETENTION_DAYS=7

# 备份文件名格式
BACKUP_NAME_FORMAT="xggl-1.0-SNAPSHOT-api.jar.backup.%Y%m%d_%H%M%S"

# =============================================================================
# 高级配置
# =============================================================================

# 部署前是否清理远程日志
CLEAN_REMOTE_LOGS=false

# 部署后是否重启相关服务（如nginx等）
RESTART_RELATED_SERVICES=false

# 相关服务列表（空格分隔）
RELATED_SERVICES=""

# 是否发送部署通知（需要配置通知方式）
SEND_NOTIFICATION=false

# 通知接收者邮箱（逗号分隔）
NOTIFICATION_EMAILS=""

# 钉钉机器人Webhook（可选）
DINGTALK_WEBHOOK=""

# 企业微信机器人Webhook（可选）
WECHAT_WEBHOOK=""

# =============================================================================
# 环境特定配置
# =============================================================================

# 部署环境标识（dev/test/prod）
DEPLOY_ENV="prod"

# 是否为生产环境
IS_PRODUCTION=true

# 生产环境额外检查
PRODUCTION_SAFETY_CHECK=true

# =============================================================================
# 自定义脚本钩子
# =============================================================================

# 部署前执行的自定义脚本（可选）
PRE_DEPLOY_SCRIPT=""

# 部署后执行的自定义脚本（可选）
POST_DEPLOY_SCRIPT=""

# 部署失败时执行的脚本（可选）
ON_FAILURE_SCRIPT=""

# =============================================================================
# 示例配置说明
# =============================================================================

# 1. 开发环境配置示例:
# SERVER_HOST="*************"
# SERVER_USER="dev"
# SERVER_PASSWORD="dev_password"
# DEPLOY_ENV="dev"
# IS_PRODUCTION=false

# 2. 测试环境配置示例:
# SERVER_HOST="*************"
# SERVER_USER="test"
# SERVER_PASSWORD="test_password"
# DEPLOY_ENV="test"
# IS_PRODUCTION=false

# 3. 生产环境配置示例:
# SERVER_HOST="**********"
# SERVER_USER="app"
# SERVER_PASSWORD=""  # 建议运行时输入，不要明文存储
# DEPLOY_ENV="prod"
# IS_PRODUCTION=true
# PRODUCTION_SAFETY_CHECK=true
# CREATE_BACKUP=true
# SEND_NOTIFICATION=true

# =============================================================================
# 注意事项
# =============================================================================

# 1. 请根据实际环境修改上述配置
# 2. 生产环境建议启用所有安全检查和备份功能
# 3. 密码建议通过环境变量设置，避免明文存储在配置文件中
# 4. 确保目标服务器上的目录结构正确
# 5. 建议在非生产环境先测试部署脚本
# 6. 需要安装sshpass工具支持密码认证
