package com.sanythadmin.common.core.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 中文转拼音工具类
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
public class PinyinUtil {
    
    private static final Pattern CHINESE_PATTERN = Pattern.compile("[\u4e00-\u9fa5]");
    private static final Pattern ENGLISH_PATTERN = Pattern.compile("[a-zA-Z]");
    private static final Pattern NUMBER_PATTERN = Pattern.compile("[0-9]");
    
    /**
     * 将中文字符串转换为拼音首字母大写的英文编码
     * 
     * @param chinese 中文字符串
     * @return 英文编码
     */
    public static String toEnglishCode(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }
        
        StringBuilder result = new StringBuilder();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);
        
        for (char c : chinese.toCharArray()) {
            if (CHINESE_PATTERN.matcher(String.valueOf(c)).matches()) {
                // 中文字符转拼音
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        String pinyin = pinyinArray[0];
                        // 首字母大写
                        result.append(pinyin.substring(0, 1).toUpperCase())
                              .append(pinyin.substring(1));
                    }
                } catch (Exception e) {
                    // 转换失败时跳过该字符
                    continue;
                }
            } else if (ENGLISH_PATTERN.matcher(String.valueOf(c)).matches()) {
                // 英文字符直接添加，首字母大写
                if (result.length() == 0 || !Character.isLetter(result.charAt(result.length() - 1))) {
                    result.append(Character.toUpperCase(c));
                } else {
                    result.append(Character.toLowerCase(c));
                }
            } else if (NUMBER_PATTERN.matcher(String.valueOf(c)).matches()) {
                // 数字直接添加
                result.append(c);
            }
            // 其他字符（标点符号等）忽略
        }
        
        return result.toString();
    }
    
    /**
     * 将中文字符串转换为简短的英文编码（取每个中文字符拼音的首字母）
     * 
     * @param chinese 中文字符串
     * @return 简短英文编码
     */
    public static String toShortEnglishCode(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }
        
        StringBuilder result = new StringBuilder();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);
        
        for (char c : chinese.toCharArray()) {
            if (CHINESE_PATTERN.matcher(String.valueOf(c)).matches()) {
                // 中文字符转拼音首字母
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        String pinyin = pinyinArray[0];
                        result.append(pinyin.substring(0, 1).toUpperCase());
                    }
                } catch (Exception e) {
                    // 转换失败时跳过该字符
                    continue;
                }
            } else if (ENGLISH_PATTERN.matcher(String.valueOf(c)).matches()) {
                // 英文字符取首字母大写
                result.append(Character.toUpperCase(c));
            } else if (NUMBER_PATTERN.matcher(String.valueOf(c)).matches()) {
                // 数字直接添加
                result.append(c);
            }
            // 其他字符（标点符号等）忽略
        }
        
        return result.toString();
    }
    
    /**
     * 生成岗位编码
     * 优先使用简短编码，如果太短则使用完整编码
     * 
     * @param jobName 岗位名称
     * @return 岗位编码
     */
    public static String generateJobCode(String jobName) {
        if (StringUtils.isBlank(jobName)) {
            return "";
        }
        
        // 先尝试生成简短编码
        String shortCode = toShortEnglishCode(jobName);
        
        // 如果简短编码长度合适（3-8位），使用简短编码
        if (shortCode.length() >= 3 && shortCode.length() <= 8) {
            return shortCode;
        }
        
        // 否则使用完整编码
        String fullCode = toEnglishCode(jobName);
        
        // 如果完整编码太长，截取前20位
        if (fullCode.length() > 20) {
            fullCode = fullCode.substring(0, 20);
        }
        
        return fullCode;
    }
}
