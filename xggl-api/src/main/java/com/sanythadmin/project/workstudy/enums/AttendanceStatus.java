package com.sanythadmin.project.workstudy.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;
import lombok.Getter;

/**
 * 打卡状态枚举
 * 暂时不考虑缺勤，根据考勤记录生成工时即可
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
public enum AttendanceStatus implements TextBaseEnum {
    NORMAL("正常打卡"),
    LATE("迟到"),
    EARLY_LEAVE("早退"),
//    ABSENT("缺勤/未打卡"),
    LEAVE("请假");

    @JsonValue
    private final String text;

    AttendanceStatus(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    /**
     * 根据文本获取枚举值
     *
     * @param text 文本
     * @return 枚举值
     */
    public static AttendanceStatus get(String text) {
        for (AttendanceStatus status : AttendanceStatus.values()) {
            if (status.getText().equals(text)) {
                return status;
            }
        }
        return null;
    }
}
