package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.project.workflow.entity.BaseApplicationInfo;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 岗位申报
 *
 * <AUTHOR>
 * @since 2025-07-15 16:32:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_JOB_APPLICATION")
@Entity
@Table(name = "SYT_QGZX_JOB_APPLICATION")
public class QgzxJobApplication extends BaseApplicationInfo {
    private static final long serialVersionUID = 1L;

    /**
     * 用人单位ID
     */
    @Column(name = "EID")
    @TableField("EID")
    private String eid;

    /**
     * 学年学期
     */
    @Column(name = "XNXQ")
    @TableField("XNXQ")
    private String xnxq;

    /**
     * 岗位编码(调剂时方便查找)
     */
    @Column(name = "JOB_CODE")
    @TableField("JOB_CODE")
    private String jobCode;
    /**
     * 岗位名称
     */
    @Column(name = "JOB_NAME")
    @TableField("JOB_NAME")
    private String jobName;

    /**
     * 岗位类别/类型(临时岗、固定岗、助教、助管 、助研、助工)
     */
    @Column(name = "JOB_TYPE_ID")
    @TableField("JOB_TYPE_ID")
    private String jobTypeId;

    /**
     * 开始日期
     */
    @Column(name = "START_DATE",columnDefinition = ColumnType.DATE)
    @TableField("START_DATE")
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Column(name = "END_DATE",columnDefinition = ColumnType.DATE)
    @TableField("END_DATE")
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate endDate;

    /**
     * 开始时间
     */
    @Column(name = "START_TIME")
    @TableField("START_TIME")
    private String startTime;

    /**
     * 结束时间
     */
    @Column(name = "END_TIME")
    @TableField("END_TIME")
    private String endTime;

    /**
     * 每月工作小时数
     */
    @Column(name = "WORK_HOUS")
    @TableField("WORK_HOUS")
    private Double workHous;

    /**
     * 时薪
     */
    @Column(name = "HOURLY_RATE")
    @TableField("HOURLY_RATE")
    private Double hourlyRate;

    /**
     * 岗位描述
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 所在校区
     */
    @Column(name = "XQMC")
    @TableField("XQMC")
    private String xqmc;

    /**
     * 联系方式
     */
    @Column(name = "LXFS")
    @TableField("LXFS")
    private String lxfs;

    /**
     * 是否对外发布
     */
    @Column(name = "PUBLISHED", columnDefinition = ColumnType.NUMBER_1)
    @TableField("PUBLISHED")
    private JudgeMark published;

    /**
     * 审批状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "SPZT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("SPZT")
    private ReviewResult spzt;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 用工人数
     */
    @Column(name = "YGRS")
    @TableField("YGRS")
    private Integer ygrs;

    /**
     * 是否校外岗
     */
    @Column(name = "SFXWG", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFXWG")
    private JudgeMark sfxwg;

    /**
     * 工作日
     */
    @Column(name = "WORK_DAYS")
    @TableField("WORK_DAYS")
    private String workDays;

    /**
     * 是否限制上课时间
     */
    @Column(name = "SFXZSKSJ", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFXZSKSJ")
    private JudgeMark sfxzsksj;

    /**
     * 是否面试
     */
    @Column(name = "SFMS", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFMS")
    private JudgeMark sfms;

    /**
     * 是否签到
     */
    @Column(name = "SFQD", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFQD")
    private JudgeMark sfqd;

    /**
     * 用工结束是否审核
     */
    @Column(name = "YGJSSFSH", columnDefinition = ColumnType.NUMBER_1)
    @TableField("YGJSSFSH")
    private JudgeMark ygjssfsh;

    /**
     * 报酬是否审核
     */
    @Column(name = "BCSFSH", columnDefinition = ColumnType.NUMBER_1)
    @TableField("BCSFSH")
    private JudgeMark bcsfsh;

    /**
     * 打卡范围（米）- 允许的打卡距离范围，默认500米
     */
    @Column(name = "DKFW", columnDefinition = "NUMBER(8)")
    @TableField("DKFW")
    private Integer dkfw;

    /**
     * 困难生比例
     */
    /*@Column(name = "PKSBL")
    @TableField("PKSBL")
    private Double pksbl;*/

    /**
     * 面试地点
     */
    @TableField("MSDD")
    private String msdd;

    /**
     * 岗位职责
     */
    @Column(name = "GWZZ")
    @TableField("GWZZ")
    private String gwzz;

    /**
     * 岗位要求
     */
    @Column(name = "GWYQ")
    @TableField("GWYQ")
    private String gwyq;

    /**
     * 招聘条件
     */
    @Column(name = "ZPTJ")
    @TableField("ZPTJ")
    private String zptj;
    /**
     * 月最高报酬
     */
    @Column(name = "YZGBC",columnDefinition = ColumnType.NUMBER_7_2)
    @TableField("YZGBC")
    private Double yzgbc;

    /**
     * 工作流ID
     */
    @Column(name = "WORKFLOW_ID")
    @TableField("WORKFLOW_ID")
    private String workflowId;

    @Transient
    @TableField(exist = false)
    private QgzxEmployer employer;

    /**
     * 岗位地址列表
     */
    @Transient
    @TableField(exist = false)
    private List<QgzxJobApplicationAddress> jobAddresses;

    /**
     * 开始结束日期是否合法
     *
     * @return
     */
    public boolean applyTimeIsCorrect() {
        return DateUtil.localDateIsCorrect(startDate, endDate);
    }
}
