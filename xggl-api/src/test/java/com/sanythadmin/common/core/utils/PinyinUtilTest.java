package com.sanythadmin.common.core.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * PinyinUtil 测试类
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
public class PinyinUtilTest {

    @Test
    public void testGenerateJobCodeExamples() {
        // 测试一些实际的岗位名称示例，验证功能正常
        assertNotNull(PinyinUtil.generateJobCode("图书管理员"));
        assertNotNull(PinyinUtil.generateJobCode("学生助理"));
        assertNotNull(PinyinUtil.generateJobCode("IT技术支持"));
        
        // 打印示例结果
        System.out.println("=== 岗位编码生成示例 ===");
        System.out.println("图书管理员 -> " + PinyinUtil.generateJobCode("图书管理员"));
        System.out.println("学生助理 -> " + PinyinUtil.generateJobCode("学生助理"));
        System.out.println("IT技术支持 -> " + PinyinUtil.generateJobCode("IT技术支持"));
        System.out.println("实验室管理员 -> " + PinyinUtil.generateJobCode("实验室管理员"));
        System.out.println("教学助理 -> " + PinyinUtil.generateJobCode("教学助理"));
    }

    @Test
    public void testEmptyInput() {
        assertEquals("", PinyinUtil.generateJobCode(""));
        assertEquals("", PinyinUtil.generateJobCode(null));
        assertEquals("", PinyinUtil.generateJobCode("   "));
    }

    @Test
    public void testConsistency() {
        // 相同输入应该产生相同输出
        String jobName = "图书管理员";
        String code1 = PinyinUtil.generateJobCode(jobName);
        String code2 = PinyinUtil.generateJobCode(jobName);
        assertEquals(code1, code2);
    }

    @Test
    public void testCodeFormat() {
        // 测试生成的编码格式
        String code = PinyinUtil.generateJobCode("学生助理");
        assertNotNull(code);
        assertFalse(code.isEmpty());
        
        // 编码应该只包含字母和数字
        assertTrue(code.matches("^[A-Za-z0-9]+$"), "编码应该只包含字母和数字，实际: " + code);
    }
}
