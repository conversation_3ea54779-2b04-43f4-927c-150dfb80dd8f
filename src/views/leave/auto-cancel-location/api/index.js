import request from '@/utils/request';

/**
 * 分页查询地理范围
 */
export async function getLeaveProjectCancelAreaPage(params) {
  const res = await request.get('/leave/project/area/page', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getLeaveProjectCancelAreaInfo(id) {
  const res = await request.get('/leave/project/area/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function operation(data) {
  const res = await request.post(
    '/leave/project/auto_cancel_location/operation',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post(
    '/leave/project/auto_cancel_location/remove',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 可选预设位置-分页查询
 */
export async function selectablePresetAddressPage(params) {
  const res = await request.get(
    '/leave/project/auto_cancel_location/selectablePresetAddressPage',
    { params }
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
