[2m2025-07-31 14:52:56.474[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-07-31 14:52:56.696[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Starting AdminApplication using Java 17.0.15 with PID 94895 (/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-xuegong-5)
[2m2025-07-31 14:52:56.697[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-07-31 14:52:56.697[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-07-31 14:52:58.191[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-07-31 14:52:58.193[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-07-31 14:52:58.537[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: jakarta.persistence.Entity, jakarta.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository
[2m2025-07-31 14:52:58.667[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 460 ms. Found 23 JPA repository interfaces.
[2m2025-07-31 14:52:58.709[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-07-31 14:52:58.709[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-07-31 14:52:58.743[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.743[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.743[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.743[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.743[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.743[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.745[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.745[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.745[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.745[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.745[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.745[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.745[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.746[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.746[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.746[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.746[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.746[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.746[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.746[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.746[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.746[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.746[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-31 14:52:58.774[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 64 ms. Found 1 MongoDB repository interface.
[2m2025-07-31 14:52:58.787[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-07-31 14:52:58.788[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-31 14:52:58.829[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.829[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.829[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.829[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.829[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.829[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.830[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.830[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.830[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.830[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.830[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.830[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.830[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.830[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.830[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.830[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.830[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.831[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.831[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.831[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.831[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.831[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.831[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.831[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-31 14:52:58.831[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
[2m2025-07-31 14:52:59.207[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
[2m2025-07-31 14:52:59.711[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8082 (http)
[2m2025-07-31 14:52:59.730[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-8082"]
[2m2025-07-31 14:52:59.732[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-07-31 14:52:59.732[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-07-31 14:52:59.780[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-07-31 14:52:59.780[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3032 ms
[2m2025-07-31 14:52:59.934[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@43f88150, com.mongodb.Jep395RecordCodecProvider@158bd27d, com.mongodb.KotlinCodecProvider@30282260]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[*************:37017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-07-31 14:53:00.104[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=88590584, minRoundTripTimeNanos=0}
[2m2025-07-31 14:53:00.267[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-07-31 14:53:00.467[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-07-31 14:53:00.472[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanythadmin.common.core.handler.DecryptInterceptor@39482f2b'
[2m2025-07-31 14:53:00.472[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@337fac72'
[2m2025-07-31 14:53:00.472[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@2174fda1'
[2m2025-07-31 14:53:00.668[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/EmailRecordMapper.xml]'
[2m2025-07-31 14:53:00.717[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-07-31 14:53:00.745[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-07-31 14:53:00.779[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-07-31 14:53:00.801[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-07-31 14:53:00.821[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysJwtMapper.xml]'
[2m2025-07-31 14:53:00.873[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-07-31 14:53:00.894[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysModuleSetupMapper.xml]'
[2m2025-07-31 14:53:00.924[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysParamMapper.xml]'
[2m2025-07-31 14:53:00.947[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-07-31 14:53:00.968[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-07-31 14:53:01.020[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-07-31 14:53:01.036[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserOrgMapMapper.xml]'
[2m2025-07-31 14:53:01.059[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinAddressMapper.xml]'
[2m2025-07-31 14:53:01.076[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemAddressMapper.xml]'
[2m2025-07-31 14:53:01.098[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemMapper.xml]'
[2m2025-07-31 14:53:01.122[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinRecordMapper.xml]'
[2m2025-07-31 14:53:01.142[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-07-31 14:53:01.163[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-07-31 14:53:01.181[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-07-31 14:53:01.198[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeMzbMapper.xml]'
[2m2025-07-31 14:53:01.218[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeNjbMapper.xml]'
[2m2025-07-31 14:53:01.234[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodePyccbMapper.xml]'
[2m2025-07-31 14:53:01.248[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-07-31 14:53:01.262[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXqbMapper.xml]'
[2m2025-07-31 14:53:01.277[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXsztMapper.xml]'
[2m2025-07-31 14:53:01.291[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-07-31 14:53:01.305[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZzmmbMapper.xml]'
[2m2025-07-31 14:53:01.318[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/ScoreSourceConfigMapper.xml]'
[2m2025-07-31 14:53:01.340[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionFieldMapper.xml]'
[2m2025-07-31 14:53:01.355[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionMapper.xml]'
[2m2025-07-31 14:53:01.372[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldEduLevelMapper.xml]'
[2m2025-07-31 14:53:01.388[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldHideRoleMapper.xml]'
[2m2025-07-31 14:53:01.406[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldLinkMapper.xml]'
[2m2025-07-31 14:53:01.429[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldMapper.xml]'
[2m2025-07-31 14:53:01.443[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupEduLevelMapper.xml]'
[2m2025-07-31 14:53:01.458[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupHideRoleMapper.xml]'
[2m2025-07-31 14:53:01.480[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupMapper.xml]'
[2m2025-07-31 14:53:01.498[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/ListGroupConfigMapper.xml]'
[2m2025-07-31 14:53:01.514[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/SelectControlApiMapper.xml]'
[2m2025-07-31 14:53:01.540[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationInfoMapper.xml]'
[2m2025-07-31 14:53:01.555[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationReferenceInfoMapper.xml]'
[2m2025-07-31 14:53:01.573[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeMapper.xml]'
[2m2025-07-31 14:53:01.592[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeRecordMapper.xml]'
[2m2025-07-31 14:53:01.606[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigMapper.xml]'
[2m2025-07-31 14:53:01.621[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigScopeMapper.xml]'
[2m2025-07-31 14:53:01.638[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailMapper.xml]'
[2m2025-07-31 14:53:01.653[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailScopeMapper.xml]'
[2m2025-07-31 14:53:01.676[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemMapper.xml]'
[2m2025-07-31 14:53:01.695[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluatePeerReviewRecordMapper.xml]'
[2m2025-07-31 14:53:01.711[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateReviewScoreMapper.xml]'
[2m2025-07-31 14:53:01.733[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreMapper.xml]'
[2m2025-07-31 14:53:01.754[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerInfoMapper.xml]'
[2m2025-07-31 14:53:01.770[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerMapper.xml]'
[2m2025-07-31 14:53:01.783[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamClassMapper.xml]'
[2m2025-07-31 14:53:01.803[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamMapper.xml]'
[2m2025-07-31 14:53:01.818[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperClassMapper.xml]'
[2m2025-07-31 14:53:01.820[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperContentMapper.xml]'
[2m2025-07-31 14:53:01.844[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperMapper.xml]'
[2m2025-07-31 14:53:01.861[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQgroupMapper.xml]'
[2m2025-07-31 14:53:01.877[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsMapper.xml]'
[2m2025-07-31 14:53:01.893[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsOptionsMapper.xml]'
[2m2025-07-31 14:53:01.907[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsRuleMapper.xml]'
[2m2025-07-31 14:53:01.920[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsClassMapper.xml]'
[2m2025-07-31 14:53:01.935[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsMapper.xml]'
[2m2025-07-31 14:53:01.948[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsOptionsMapper.xml]'
[2m2025-07-31 14:53:01.961[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamRecordMapper.xml]'
[2m2025-07-31 14:53:01.982[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationInfoMapper.xml]'
[2m2025-07-31 14:53:01.998[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationListInfoMapper.xml]'
[2m2025-07-31 14:53:02.021[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeMapper.xml]'
[2m2025-07-31 14:53:02.040[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeRecordMapper.xml]'
[2m2025-07-31 14:53:02.059[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalResultMapper.xml]'
[2m2025-07-31 14:53:02.075[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormCustomFieldMapper.xml]'
[2m2025-07-31 14:53:02.092[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormGroupMapper.xml]'
[2m2025-07-31 14:53:02.107[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormLimitQuotaMapper.xml]'
[2m2025-07-31 14:53:02.126[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectMapper.xml]'
[2m2025-07-31 14:53:02.139[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectSpecialListMapper.xml]'
[2m2025-07-31 14:53:02.154[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectTemplateFieldMapper.xml]'
[2m2025-07-31 14:53:02.174[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityContentMapper.xml]'
[2m2025-07-31 14:53:02.192[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityFeedbackMapper.xml]'
[2m2025-07-31 14:53:02.208[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityItemMapper.xml]'
[2m2025-07-31 14:53:02.227[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormRestrictMapper.xml]'
[2m2025-07-31 14:53:02.244[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldLinkMapper.xml]'
[2m2025-07-31 14:53:02.261[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldMapper.xml]'
[2m2025-07-31 14:53:02.276[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTypeMapper.xml]'
[2m2025-07-31 14:53:02.294[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoLogMapper.xml]'
[2m2025-07-31 14:53:02.312[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoMapper.xml]'
[2m2025-07-31 14:53:02.326[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkConsultantMapper.xml]'
[2m2025-07-31 14:53:02.342[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselingRecordMapper.xml]'
[2m2025-07-31 14:53:02.358[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselorScheduleMapper.xml]'
[2m2025-07-31 14:53:02.373[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportApprovalNodeMapper.xml]'
[2m2025-07-31 14:53:02.398[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportMapper.xml]'
[2m2025-07-31 14:53:02.413[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportNodeMapper.xml]'
[2m2025-07-31 14:53:02.434[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisRepositoryMapper.xml]'
[2m2025-07-31 14:53:02.454[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkFollowupRecordMapper.xml]'
[2m2025-07-31 14:53:02.470[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkInterpretationMapper.xml]'
[2m2025-07-31 14:53:02.484[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkParamMapper.xml]'
[2m2025-07-31 14:53:02.501[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerInfoMapper.xml]'
[2m2025-07-31 14:53:02.519[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerMapper.xml]'
[2m2025-07-31 14:53:02.533[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyInterpretationMapper.xml]'
[2m2025-07-31 14:53:02.547[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyMapper.xml]'
[2m2025-07-31 14:53:02.560[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyReportMapper.xml]'
[2m2025-07-31 14:53:02.577[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeDataConfigMapper.xml]'
[2m2025-07-31 14:53:02.597[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateMapper.xml]'
[2m2025-07-31 14:53:02.612[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateTypeMapper.xml]'
[2m2025-07-31 14:53:02.628[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/UserResumeMapper.xml]'
[2m2025-07-31 14:53:02.648[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseMarkMapper.xml]'
[2m2025-07-31 14:53:02.663[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CoursePropertiesPercentMapper.xml]'
[2m2025-07-31 14:53:02.683[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreItemMapper.xml]'
[2m2025-07-31 14:53:02.706[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreResultMapper.xml]'
[2m2025-07-31 14:53:02.723[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreLycjMapper.xml]'
[2m2025-07-31 14:53:02.738[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreMycjMapper.xml]'
[2m2025-07-31 14:53:02.752[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTycjMapper.xml]'
[2m2025-07-31 14:53:02.766[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTyhdcyjfMapper.xml]'
[2m2025-07-31 14:53:02.781[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTzjkcsMapper.xml]'
[2m2025-07-31 14:53:02.802[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorAccountMapper.xml]'
[2m2025-07-31 14:53:02.818[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorConfigMapper.xml]'
[2m2025-07-31 14:53:02.833[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorDataMapper.xml]'
[2m2025-07-31 14:53:02.850[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerInfoMapper.xml]'
[2m2025-07-31 14:53:02.866[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerMapper.xml]'
[2m2025-07-31 14:53:02.880[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyClassMapper.xml]'
[2m2025-07-31 14:53:02.899[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyMapper.xml]'
[2m2025-07-31 14:53:02.917[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsMapper.xml]'
[2m2025-07-31 14:53:02.934[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsOptionsMapper.xml]'
[2m2025-07-31 14:53:02.948[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsRuleMapper.xml]'
[2m2025-07-31 14:53:02.961[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQgroupMapper.xml]'
[2m2025-07-31 14:53:02.974[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsClassMapper.xml]'
[2m2025-07-31 14:53:02.990[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsMapper.xml]'
[2m2025-07-31 14:53:03.011[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsOptionsMapper.xml]'
[2m2025-07-31 14:53:03.032[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerInfoMapper.xml]'
[2m2025-07-31 14:53:03.053[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerMapper.xml]'
[2m2025-07-31 14:53:03.070[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpGradeMapper.xml]'
[2m2025-07-31 14:53:03.087[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemMapper.xml]'
[2m2025-07-31 14:53:03.101[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemStaffMapper.xml]'
[2m2025-07-31 14:53:03.117[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpResultMapper.xml]'
[2m2025-07-31 14:53:03.133[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemMapper.xml]'
[2m2025-07-31 14:53:03.147[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemStaffMapper.xml]'
[2m2025-07-31 14:53:03.166[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemFieldMapper.xml]'
[2m2025-07-31 14:53:03.179[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemMapper.xml]'
[2m2025-07-31 14:53:03.198[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemRecordMapper.xml]'
[2m2025-07-31 14:53:03.213[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/QuickSearchMapper.xml]'
[2m2025-07-31 14:53:03.229[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/StudentRecordChangeMapper.xml]'
[2m2025-07-31 14:53:03.243[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserDataScopeMapper.xml]'
[2m2025-07-31 14:53:03.267[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserListInfoMapper.xml]'
[2m2025-07-31 14:53:03.288[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeMapper.xml]'
[2m2025-07-31 14:53:03.306[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeRecordMapper.xml]'
[2m2025-07-31 14:53:03.324[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionDetailMapper.xml]'
[2m2025-07-31 14:53:03.339[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionMapper.xml]'
[2m2025-07-31 14:53:03.357[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowEventMapper.xml]'
[2m2025-07-31 14:53:03.370[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowMapper.xml]'
[2m2025-07-31 14:53:03.386[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeApproverMapper.xml]'
[2m2025-07-31 14:53:03.401[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeFormMapper.xml]'
[2m2025-07-31 14:53:03.415[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeMapper.xml]'
[2m2025-07-31 14:53:03.430[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeStateMapper.xml]'
[2m2025-07-31 14:53:03.446[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxEmployerMapper.xml]'
[2m2025-07-31 14:53:03.465[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxInterviewRecordMapper.xml]'
[2m2025-07-31 14:53:03.479[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxJobTypeMapper.xml]'
[2m2025-07-31 14:53:03.494[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxKsbMapper.xml]'
[2m2025-07-31 14:53:03.518[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxStudentsClassTimeMapper.xml]'
[2m2025-07-31 14:53:03.530[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:9
[2m2025-07-31 14:53:03.850[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-07-31 14:53:03.928[0;39m [31mERROR[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-07-31 14:53:04.280[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-07-31 14:53:08.016[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-07-31 14:53:08.219[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.c.s.JwtAuthenticationFilter       [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-07-31 14:53:08.426[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-07-31 14:53:08.474[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-07-31 14:53:08.523[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-07-31 14:53:08.766[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-07-31 14:53:08.857[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-07-31 14:53:10.278[0;39m [33m WARN[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-07-31 14:53:10.523[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-07-31 14:53:00",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:629328399, ConnectTime:"2025-07-31 14:53:09", UseCount:1, LastActiveTime:"2025-07-31 14:53:10"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-07-31 14:53:13.111[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-07-31 14:53:13.119[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-07-31 14:53:13.521[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m JSqlParser is in classpath; If applicable, JSqlParser will be used.
[2m2025-07-31 14:53:13.521[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m Hibernate is in classpath; If applicable, HQL parser will be used.
[2m2025-07-31 14:53:22.788[0;39m [33m WARN[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 53b1ea5c-b315-4caf-9832-4c1034264a9d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-07-31 14:53:22.801[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-07-31 14:53:23.340[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 53b1ea5c-b315-4caf-9832-4c1034264a9d

[2m2025-07-31 14:53:23.627[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-8082"]
[2m2025-07-31 14:53:23.665[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8082 (http) with context path '/'
[2m2025-07-31 14:53:23.681[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Started AdminApplication in 27.794 seconds (process running for 29.94)
[2m2025-07-31 14:53:23.684[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-07-31 14:53:23.685[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-07-31 14:53:26.286[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-07-31 14:53:26.347[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-07-31 14:53:26.352[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-07-31 14:53:26.369[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-07-31 14:53:26.413[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-31 14:53:26.500[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 14:53:28.163[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-07-31 14:53:28.163[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-31 14:53:28.168[0;39m [32m INFO[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 5 ms
[2m2025-07-31 14:53:28.227[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 14:53:28.243[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 14:53:28.245[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 14:53:28.246[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 14:53:28.248[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生处(String)
[2m2025-07-31 14:53:28.339[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 14:53:28.743[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 14:53:28.752[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 14:53:28.756[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 14:53:28.757[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 14:53:28.758[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-07-31 14:53:28.833[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 14:53:28.834[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 14:53:28.842[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 14:53:28.845[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 14:53:28.845[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 14:53:28.846[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-07-31 14:53:28.919[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-31 14:53:29.958[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT     t.ID,t.STUDENT_APPLY_ID,t.ATTENDANCE_DATE,t.ATTENDANCE_TYPE,t.CLOCK_TIME,t.LOCATION,t.LONGITUDE,t.LATITUDE,t.WORK_DURATION,t.IS_NORMAL,t.ATTENDANCE_STATUS,t.REMARK,t.CREATE_TIME,t.UPDATE_TIME,e.NAME AS employerName,u.XM AS studentName,u.SJH AS studentContact,j.JOB_NAME AS jobName,sa.ID AS joina_ID,sa.XGH,sa.JOB_ID,sa.XNXQ,sa.SQSJ,sa.sqly,sa.TCYS,sa.SFFCAP,sa.SPZT,sa.YGZT,sa.WORKFLOW_ID,sa.TJZT,sa.ORIGINAL_JOB_ID,sa.TJSQSJ,sa.TJQRSJ,sa.XSQRSJ,sa.ROLE_ID,sa.XXMC,sa.SFTS,u.XGH AS joina_XGH,u.BZ1,u.BZ2,u.BZ3,u.BZ4,u.BZ5,u.BZ6,u.BZ7,u.BZ8,u.BZ9,u.BZ10,u.BZ11,u.BZ12,u.BZ13,u.BZ14,u.BZ15,u.BZ16,u.BZ17,u.BZ18,u.BZ19,u.BZ20,u.BZ21,u.BZ22,u.BZ23,u.BZ24,u.BZ25,u.BZ26,u.BZ27,u.BZ28,u.BZ29,u.BZ30,u.BZ31,u.BZ32,u.BZ33,u.BZ34,u.BZ35,u.BZ36,u.BZ37,u.BZ38,u.BZ39,u.BZ40,u.BZ41,u.BZ42,u.BZ43,u.BZ44,u.BZ45,u.BZ46,u.BZ47,u.BZ48,u.BZ49,u.BZ50,u.BZ51,u.BZ52,u.BZ53,u.BZ54,u.BZ55,u.BZ56,u.BZ57,u.BZ58,u.BZ59,u.BZ60,u.BZ61,u.BZ62,u.BZ63,u.BZ64,u.BZ65,u.BZ66,u.BZ67,u.BZ68,u.BZ69,u.BZ70,u.BZ71,u.BZ72,u.BZ73,u.BZ74,u.BZ75,u.BZ76,u.BZ77,u.BZ78,u.BZ79,u.BZ80,u.BZ81,u.BZ82,u.BZ83,u.BZ84,u.BZ85,u.BZ86,u.BZ87,u.BZ88,u.BZ89,u.BZ90,u.BZ91,u.BZ92,u.BZ93,u.BZ94,u.BZ95,u.BZ96,u.BZ97,u.BZ98,u.BZ99,u.BZ100,u.XM,u.XB,u.SJH,u.CSRQ,u.JG,u.MZMC,u.ZZMMMC,u.XQMC,u.ZJHM,u.ZJLX,u.XSLB,u.XZLX,u.RYZTID,u.USER_TYPE,u.PHOTO,j.ID AS joinb_ID,j.EID,j.XNXQ AS joina_XNXQ,j.JOB_NAME,j.JOB_TYPE_ID,j.START_DATE,j.END_DATE,j.START_TIME,j.END_TIME,j.WORK_HOUS,j.HOURLY_RATE,j.BZ,j.XQMC AS joina_XQMC,j.LXFS,j.PUBLISHED,j.SPZT AS joina_SPZT,j.CREATE_TIME AS joina_CREATE_TIME,j.YGRS,j.SFXWG,j.WORK_DAYS,j.SFXZSKSJ,j.SFMS,j.SFQD,j.YGJSSFSH,j.BCSFSH,j.DKFW,j.MSDD,j.GWZZ,j.GWYQ,j.ZPTJ,j.YZGBC,j.WORKFLOW_ID AS joina_WORKFLOW_ID,j.XGH AS joinb_XGH,j.ROLE_ID AS joina_ROLE_ID,j.XXMC AS joina_XXMC,j.SFTS AS joina_SFTS   FROM SYT_QGZX_ATTENDANCE_RECORD  t    LEFT JOIN SYT_QGZX_STUDENT_APPLY sa ON (sa.ID = t.STUDENT_APPLY_ID) LEFT JOIN SYT_USER_INFO u ON (u.XGH = sa.XGH) LEFT JOIN SYT_QGZX_JOB_APPLICATION j ON (j.ID = sa.JOB_ID) LEFT JOIN SYT_QGZX_EMPLOYER e ON (e.ID = j.EID)     WHERE   (t.ATTENDANCE_STATUS IN (?,?)) ORDER BY t.CREATE_TIME DESC
[2m2025-07-31 14:53:29.996[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT     t.ID,t.STUDENT_APPLY_ID,t.ATTENDANCE_DATE,t.ATTENDANCE_TYPE,t.CLOCK_TIME,t.LOCATION,t.LONGITUDE,t.LATITUDE,t.WORK_DURATION,t.IS_NORMAL,t.ATTENDANCE_STATUS,t.REMARK,t.CREATE_TIME,t.UPDATE_TIME,e.NAME AS employerName,u.XM AS studentName,u.SJH AS studentContact,j.JOB_NAME AS jobName,sa.ID AS joina_ID,sa.XGH,sa.JOB_ID,sa.XNXQ,sa.SQSJ,sa.sqly,sa.TCYS,sa.SFFCAP,sa.SPZT,sa.YGZT,sa.WORKFLOW_ID,sa.TJZT,sa.ORIGINAL_JOB_ID,sa.TJSQSJ,sa.TJQRSJ,sa.XSQRSJ,sa.ROLE_ID,sa.XXMC,sa.SFTS,u.XGH AS joina_XGH,u.BZ1,u.BZ2,u.BZ3,u.BZ4,u.BZ5,u.BZ6,u.BZ7,u.BZ8,u.BZ9,u.BZ10,u.BZ11,u.BZ12,u.BZ13,u.BZ14,u.BZ15,u.BZ16,u.BZ17,u.BZ18,u.BZ19,u.BZ20,u.BZ21,u.BZ22,u.BZ23,u.BZ24,u.BZ25,u.BZ26,u.BZ27,u.BZ28,u.BZ29,u.BZ30,u.BZ31,u.BZ32,u.BZ33,u.BZ34,u.BZ35,u.BZ36,u.BZ37,u.BZ38,u.BZ39,u.BZ40,u.BZ41,u.BZ42,u.BZ43,u.BZ44,u.BZ45,u.BZ46,u.BZ47,u.BZ48,u.BZ49,u.BZ50,u.BZ51,u.BZ52,u.BZ53,u.BZ54,u.BZ55,u.BZ56,u.BZ57,u.BZ58,u.BZ59,u.BZ60,u.BZ61,u.BZ62,u.BZ63,u.BZ64,u.BZ65,u.BZ66,u.BZ67,u.BZ68,u.BZ69,u.BZ70,u.BZ71,u.BZ72,u.BZ73,u.BZ74,u.BZ75,u.BZ76,u.BZ77,u.BZ78,u.BZ79,u.BZ80,u.BZ81,u.BZ82,u.BZ83,u.BZ84,u.BZ85,u.BZ86,u.BZ87,u.BZ88,u.BZ89,u.BZ90,u.BZ91,u.BZ92,u.BZ93,u.BZ94,u.BZ95,u.BZ96,u.BZ97,u.BZ98,u.BZ99,u.BZ100,u.XM,u.XB,u.SJH,u.CSRQ,u.JG,u.MZMC,u.ZZMMMC,u.XQMC,u.ZJHM,u.ZJLX,u.XSLB,u.XZLX,u.RYZTID,u.USER_TYPE,u.PHOTO,j.ID AS joinb_ID,j.EID,j.XNXQ AS joina_XNXQ,j.JOB_NAME,j.JOB_TYPE_ID,j.START_DATE,j.END_DATE,j.START_TIME,j.END_TIME,j.WORK_HOUS,j.HOURLY_RATE,j.BZ,j.XQMC AS joina_XQMC,j.LXFS,j.PUBLISHED,j.SPZT AS joina_SPZT,j.CREATE_TIME AS joina_CREATE_TIME,j.YGRS,j.SFXWG,j.WORK_DAYS,j.SFXZSKSJ,j.SFMS,j.SFQD,j.YGJSSFSH,j.BCSFSH,j.DKFW,j.MSDD,j.GWZZ,j.GWYQ,j.ZPTJ,j.YZGBC,j.WORKFLOW_ID AS joina_WORKFLOW_ID,j.XGH AS joinb_XGH,j.ROLE_ID AS joina_ROLE_ID,j.XXMC AS joina_XXMC,j.SFTS AS joina_SFTS   FROM SYT_QGZX_ATTENDANCE_RECORD  t    LEFT JOIN SYT_QGZX_STUDENT_APPLY sa ON (sa.ID = t.STUDENT_APPLY_ID) LEFT JOIN SYT_USER_INFO u ON (u.XGH = sa.XGH) LEFT JOIN SYT_QGZX_JOB_APPLICATION j ON (j.ID = sa.JOB_ID) LEFT JOIN SYT_QGZX_EMPLOYER e ON (e.ID = j.EID)     WHERE   (t.ATTENDANCE_STATUS IN (?,?)) ORDER BY t.CREATE_TIME DESC
[2m2025-07-31 14:53:29.999[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT t.ID, t.STUDENT_APPLY_ID, t.ATTENDANCE_DATE, t.ATTENDANCE_TYPE, t.CLOCK_TIME, t.LOCATION, t.LONGITUDE, t.LATITUDE, t.WORK_DURATION, t.IS_NORMAL, t.ATTENDANCE_STATUS, t.REMARK, t.CREATE_TIME, t.UPDATE_TIME, e.NAME AS employerName, u.XM AS studentName, u.SJH AS studentContact, j.JOB_NAME AS jobName, sa.ID AS joina_ID, sa.XGH, sa.JOB_ID, sa.XNXQ, sa.SQSJ, sa.sqly, sa.TCYS, sa.SFFCAP, sa.SPZT, sa.YGZT, sa.WORKFLOW_ID, sa.TJZT, sa.ORIGINAL_JOB_ID, sa.TJSQSJ, sa.TJQRSJ, sa.XSQRSJ, sa.ROLE_ID, sa.XXMC, sa.SFTS, u.XGH AS joina_XGH, u.BZ1, u.BZ2, u.BZ3, u.BZ4, u.BZ5, u.BZ6, u.BZ7, u.BZ8, u.BZ9, u.BZ10, u.BZ11, u.BZ12, u.BZ13, u.BZ14, u.BZ15, u.BZ16, u.BZ17, u.BZ18, u.BZ19, u.BZ20, u.BZ21, u.BZ22, u.BZ23, u.BZ24, u.BZ25, u.BZ26, u.BZ27, u.BZ28, u.BZ29, u.BZ30, u.BZ31, u.BZ32, u.BZ33, u.BZ34, u.BZ35, u.BZ36, u.BZ37, u.BZ38, u.BZ39, u.BZ40, u.BZ41, u.BZ42, u.BZ43, u.BZ44, u.BZ45, u.BZ46, u.BZ47, u.BZ48, u.BZ49, u.BZ50, u.BZ51, u.BZ52, u.BZ53, u.BZ54, u.BZ55, u.BZ56, u.BZ57, u.BZ58, u.BZ59, u.BZ60, u.BZ61, u.BZ62, u.BZ63, u.BZ64, u.BZ65, u.BZ66, u.BZ67, u.BZ68, u.BZ69, u.BZ70, u.BZ71, u.BZ72, u.BZ73, u.BZ74, u.BZ75, u.BZ76, u.BZ77, u.BZ78, u.BZ79, u.BZ80, u.BZ81, u.BZ82, u.BZ83, u.BZ84, u.BZ85, u.BZ86, u.BZ87, u.BZ88, u.BZ89, u.BZ90, u.BZ91, u.BZ92, u.BZ93, u.BZ94, u.BZ95, u.BZ96, u.BZ97, u.BZ98, u.BZ99, u.BZ100, u.XM, u.XB, u.SJH, u.CSRQ, u.JG, u.MZMC, u.ZZMMMC, u.XQMC, u.ZJHM, u.ZJLX, u.XSLB, u.XZLX, u.RYZTID, u.USER_TYPE, u.PHOTO, j.ID AS joinb_ID, j.EID, j.XNXQ AS joina_XNXQ, j.JOB_NAME, j.JOB_TYPE_ID, j.START_DATE, j.END_DATE, j.START_TIME, j.END_TIME, j.WORK_HOUS, j.HOURLY_RATE, j.BZ, j.XQMC AS joina_XQMC, j.LXFS, j.PUBLISHED, j.SPZT AS joina_SPZT, j.CREATE_TIME AS joina_CREATE_TIME, j.YGRS, j.SFXWG, j.WORK_DAYS, j.SFXZSKSJ, j.SFMS, j.SFQD, j.YGJSSFSH, j.BCSFSH, j.DKFW, j.MSDD, j.GWZZ, j.GWYQ, j.ZPTJ, j.YZGBC, j.WORKFLOW_ID AS joina_WORKFLOW_ID, j.XGH AS joinb_XGH, j.ROLE_ID AS joina_ROLE_ID, j.XXMC AS joina_XXMC, j.SFTS AS joina_SFTS FROM SYT_QGZX_ATTENDANCE_RECORD t LEFT JOIN SYT_QGZX_STUDENT_APPLY sa ON (sa.ID = t.STUDENT_APPLY_ID) LEFT JOIN SYT_USER_INFO u ON (u.XGH = sa.XGH) LEFT JOIN SYT_QGZX_JOB_APPLICATION j ON (j.ID = sa.JOB_ID) LEFT JOIN SYT_QGZX_EMPLOYER e ON (e.ID = j.EID) WHERE (t.ATTENDANCE_STATUS IN (?, ?)) ORDER BY t.CREATE_TIME DESC
[2m2025-07-31 14:53:30.046[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.selectJoinPage_mpCount      [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_QGZX_ATTENDANCE_RECORD t WHERE (t.ATTENDANCE_STATUS IN (?, ?))
[2m2025-07-31 14:53:30.046[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.selectJoinPage_mpCount      [0;39m [2m:[0;39m ==> Parameters: LATE(String), EARLY_LEAVE(String)
[2m2025-07-31 14:53:30.129[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.selectJoinPage_mpCount      [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 14:53:30.135[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.selectJoinPage              [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT t.ID, t.STUDENT_APPLY_ID, t.ATTENDANCE_DATE, t.ATTENDANCE_TYPE, t.CLOCK_TIME, t.LOCATION, t.LONGITUDE, t.LATITUDE, t.WORK_DURATION, t.IS_NORMAL, t.ATTENDANCE_STATUS, t.REMARK, t.CREATE_TIME, t.UPDATE_TIME, e.NAME AS employerName, u.XM AS studentName, u.SJH AS studentContact, j.JOB_NAME AS jobName, sa.ID AS joina_ID, sa.XGH, sa.JOB_ID, sa.XNXQ, sa.SQSJ, sa.sqly, sa.TCYS, sa.SFFCAP, sa.SPZT, sa.YGZT, sa.WORKFLOW_ID, sa.TJZT, sa.ORIGINAL_JOB_ID, sa.TJSQSJ, sa.TJQRSJ, sa.XSQRSJ, sa.ROLE_ID, sa.XXMC, sa.SFTS, u.XGH AS joina_XGH, u.BZ1, u.BZ2, u.BZ3, u.BZ4, u.BZ5, u.BZ6, u.BZ7, u.BZ8, u.BZ9, u.BZ10, u.BZ11, u.BZ12, u.BZ13, u.BZ14, u.BZ15, u.BZ16, u.BZ17, u.BZ18, u.BZ19, u.BZ20, u.BZ21, u.BZ22, u.BZ23, u.BZ24, u.BZ25, u.BZ26, u.BZ27, u.BZ28, u.BZ29, u.BZ30, u.BZ31, u.BZ32, u.BZ33, u.BZ34, u.BZ35, u.BZ36, u.BZ37, u.BZ38, u.BZ39, u.BZ40, u.BZ41, u.BZ42, u.BZ43, u.BZ44, u.BZ45, u.BZ46, u.BZ47, u.BZ48, u.BZ49, u.BZ50, u.BZ51, u.BZ52, u.BZ53, u.BZ54, u.BZ55, u.BZ56, u.BZ57, u.BZ58, u.BZ59, u.BZ60, u.BZ61, u.BZ62, u.BZ63, u.BZ64, u.BZ65, u.BZ66, u.BZ67, u.BZ68, u.BZ69, u.BZ70, u.BZ71, u.BZ72, u.BZ73, u.BZ74, u.BZ75, u.BZ76, u.BZ77, u.BZ78, u.BZ79, u.BZ80, u.BZ81, u.BZ82, u.BZ83, u.BZ84, u.BZ85, u.BZ86, u.BZ87, u.BZ88, u.BZ89, u.BZ90, u.BZ91, u.BZ92, u.BZ93, u.BZ94, u.BZ95, u.BZ96, u.BZ97, u.BZ98, u.BZ99, u.BZ100, u.XM, u.XB, u.SJH, u.CSRQ, u.JG, u.MZMC, u.ZZMMMC, u.XQMC, u.ZJHM, u.ZJLX, u.XSLB, u.XZLX, u.RYZTID, u.USER_TYPE, u.PHOTO, j.ID AS joinb_ID, j.EID, j.XNXQ AS joina_XNXQ, j.JOB_NAME, j.JOB_TYPE_ID, j.START_DATE, j.END_DATE, j.START_TIME, j.END_TIME, j.WORK_HOUS, j.HOURLY_RATE, j.BZ, j.XQMC AS joina_XQMC, j.LXFS, j.PUBLISHED, j.SPZT AS joina_SPZT, j.CREATE_TIME AS joina_CREATE_TIME, j.YGRS, j.SFXWG, j.WORK_DAYS, j.SFXZSKSJ, j.SFMS, j.SFQD, j.YGJSSFSH, j.BCSFSH, j.DKFW, j.MSDD, j.GWZZ, j.GWYQ, j.ZPTJ, j.YZGBC, j.WORKFLOW_ID AS joina_WORKFLOW_ID, j.XGH AS joinb_XGH, j.ROLE_ID AS joina_ROLE_ID, j.XXMC AS joina_XXMC, j.SFTS AS joina_SFTS FROM SYT_QGZX_ATTENDANCE_RECORD t LEFT JOIN SYT_QGZX_STUDENT_APPLY sa ON (sa.ID = t.STUDENT_APPLY_ID) LEFT JOIN SYT_USER_INFO u ON (u.XGH = sa.XGH) LEFT JOIN SYT_QGZX_JOB_APPLICATION j ON (j.ID = sa.JOB_ID) LEFT JOIN SYT_QGZX_EMPLOYER e ON (e.ID = j.EID) WHERE (t.ATTENDANCE_STATUS IN (?, ?)) ORDER BY t.CREATE_TIME DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-31 14:53:30.138[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.selectJoinPage              [0;39m [2m:[0;39m ==> Parameters: LATE(String), EARLY_LEAVE(String), 20(Long), 0(Long)
[2m2025-07-31 14:53:30.394[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.selectJoinPage              [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-31 15:15:08.776[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:15:08.786[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:15:08.788[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:15:08.878[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:15:08.879[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-07-31 15:15:08.962[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:15:09.301[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:15:09.309[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:15:09.311[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:15:09.312[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:15:09.312[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xh006(String), xh006(String)
[2m2025-07-31 15:15:09.390[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:15:09.397[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:15:09.404[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:15:09.410[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:15:09.411[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:15:09.411[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 019c96da420aba1d4925a9090357af14(String)
[2m2025-07-31 15:15:09.487[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-31 15:15:10.247[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:15:10.254[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:15:10.256[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:15:10.257[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:15:10.257[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-07-31 15:15:10.335[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-31 15:15:10.643[0;39m [31mERROR[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.c.handler.GlobalExceptionHandler  [0;39m [2m:[0;39m 学生申请不存在

com.sanythadmin.common.core.exception.BusinessException: 学生申请不存在
	at com.sanythadmin.common.core.utils.AssertUtil.isTrue(AssertUtil.java:37)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl.clockOut(QgzxAttendanceRecordServiceImpl.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl$$SpringCGLIB$$0.clockOut(<generated>)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController.clockOut(QgzxAttendanceRecordController.java:80)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController$$SpringCGLIB$$0.clockOut(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-07-31 15:16:09.252[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:16:09.263[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:16:09.265[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:16:09.266[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:16:09.267[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-07-31 15:16:09.340[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:16:09.645[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:16:09.654[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:16:09.656[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:16:09.659[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:16:09.661[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xh006(String), xh006(String)
[2m2025-07-31 15:16:09.732[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:16:09.735[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:16:09.741[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:16:09.744[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:16:09.744[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:16:09.745[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 019c96da420aba1d4925a9090357af14(String)
[2m2025-07-31 15:16:09.820[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-31 15:16:10.660[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:16:10.667[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:16:10.669[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:16:10.669[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:16:10.669[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-07-31 15:16:10.733[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-31 15:16:10.828[0;39m [31mERROR[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.c.handler.GlobalExceptionHandler  [0;39m [2m:[0;39m 学生申请不存在

com.sanythadmin.common.core.exception.BusinessException: 学生申请不存在
	at com.sanythadmin.common.core.utils.AssertUtil.isTrue(AssertUtil.java:37)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl.clockOut(QgzxAttendanceRecordServiceImpl.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl$$SpringCGLIB$$0.clockOut(<generated>)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController.clockOut(QgzxAttendanceRecordController.java:80)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController$$SpringCGLIB$$0.clockOut(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-07-31 15:17:48.604[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:17:48.616[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:17:48.619[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:17:48.692[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:17:48.693[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-07-31 15:17:48.770[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:17:49.076[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:17:49.084[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:17:49.087[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:17:49.092[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:17:49.093[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: **********(String), **********(String)
[2m2025-07-31 15:17:49.163[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:17:49.165[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:17:49.171[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:17:49.173[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:17:49.173[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:17:49.173[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: f8d43934c2a0307c955af06ba196813a(String)
[2m2025-07-31 15:17:49.238[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:17:50.064[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:17:50.071[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:17:50.073[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:17:50.073[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:17:50.073[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-07-31 15:17:50.153[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-31 15:17:50.285[0;39m [31mERROR[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.c.handler.GlobalExceptionHandler  [0;39m [2m:[0;39m 学生申请不存在

com.sanythadmin.common.core.exception.BusinessException: 学生申请不存在
	at com.sanythadmin.common.core.utils.AssertUtil.isTrue(AssertUtil.java:37)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl.clockOut(QgzxAttendanceRecordServiceImpl.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl$$SpringCGLIB$$0.clockOut(<generated>)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController.clockOut(QgzxAttendanceRecordController.java:80)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController$$SpringCGLIB$$0.clockOut(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-07-31 15:22:47.350[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:22:47.364[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:22:47.367[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:22:47.442[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:22:47.443[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-07-31 15:22:47.524[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:22:48.203[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:22:48.218[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:22:48.229[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:22:48.230[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:22:48.230[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: **********(String), **********(String)
[2m2025-07-31 15:22:48.321[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:22:48.327[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:22:48.342[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:22:48.353[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:22:48.354[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:22:48.354[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: f8d43934c2a0307c955af06ba196813a(String)
[2m2025-07-31 15:22:48.435[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:22:49.145[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:22:49.160[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:22:49.162[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:22:49.162[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:22:49.162[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-07-31 15:22:49.234[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-31 15:22:49.309[0;39m [31mERROR[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.c.handler.GlobalExceptionHandler  [0;39m [2m:[0;39m 学生申请不存在

com.sanythadmin.common.core.exception.BusinessException: 学生申请不存在
	at com.sanythadmin.common.core.utils.AssertUtil.isTrue(AssertUtil.java:37)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl.clockOut(QgzxAttendanceRecordServiceImpl.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl$$SpringCGLIB$$0.clockOut(<generated>)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController.clockOut(QgzxAttendanceRecordController.java:80)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController$$SpringCGLIB$$0.clockOut(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-07-31 15:23:24.872[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:23:24.883[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:23:24.887[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:23:24.889[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:23:24.891[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-07-31 15:23:24.975[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:23:25.297[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:23:25.305[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:23:25.307[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:23:25.308[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:23:25.308[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: **********(String), **********(String)
[2m2025-07-31 15:23:25.378[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:23:25.381[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:23:25.387[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:23:25.389[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:23:25.390[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:23:25.390[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: f8d43934c2a0307c955af06ba196813a(String)
[2m2025-07-31 15:23:25.459[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:23:26.108[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:23:26.116[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:23:26.118[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:23:26.118[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:23:26.119[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 31f18466f850463284198c847c0ea8f6(String)
[2m2025-07-31 15:23:26.193[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:23:26.196[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,EID,XNXQ,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-07-31 15:23:26.207[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,EID,XNXQ,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-07-31 15:23:26.210[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, EID, XNXQ, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-07-31 15:23:26.210[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, EID, XNXQ, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-07-31 15:23:26.211[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-07-31 15:23:26.281[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:23:26.285[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,STUDENT_APPLY_ID,ATTENDANCE_DATE,ATTENDANCE_TYPE,CLOCK_TIME,LOCATION,LONGITUDE,LATITUDE,WORK_DURATION,IS_NORMAL,ATTENDANCE_STATUS,REMARK,CREATE_TIME,UPDATE_TIME    FROM  SYT_QGZX_ATTENDANCE_RECORD         WHERE  (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:23:26.297[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,STUDENT_APPLY_ID,ATTENDANCE_DATE,ATTENDANCE_TYPE,CLOCK_TIME,LOCATION,LONGITUDE,LATITUDE,WORK_DURATION,IS_NORMAL,ATTENDANCE_STATUS,REMARK,CREATE_TIME,UPDATE_TIME    FROM  SYT_QGZX_ATTENDANCE_RECORD         WHERE  (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:23:26.299[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, STUDENT_APPLY_ID, ATTENDANCE_DATE, ATTENDANCE_TYPE, CLOCK_TIME, LOCATION, LONGITUDE, LATITUDE, WORK_DURATION, IS_NORMAL, ATTENDANCE_STATUS, REMARK, CREATE_TIME, UPDATE_TIME FROM SYT_QGZX_ATTENDANCE_RECORD WHERE (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:23:26.299[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, STUDENT_APPLY_ID, ATTENDANCE_DATE, ATTENDANCE_TYPE, CLOCK_TIME, LOCATION, LONGITUDE, LATITUDE, WORK_DURATION, IS_NORMAL, ATTENDANCE_STATUS, REMARK, CREATE_TIME, UPDATE_TIME FROM SYT_QGZX_ATTENDANCE_RECORD WHERE (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:23:26.305[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m ==> Parameters: 31f18466f850463284198c847c0ea8f6(String), CLOCK_IN(String), 2025-07-31T00:00(LocalDateTime), 2025-07-31T23:59:59(LocalDateTime)
[2m2025-07-31 15:23:26.389[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-31 15:23:26.500[0;39m [31mERROR[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.c.handler.GlobalExceptionHandler  [0;39m [2m:[0;39m 今日尚未上班打卡，无法下班打卡

com.sanythadmin.common.core.exception.BusinessException: 今日尚未上班打卡，无法下班打卡
	at com.sanythadmin.common.core.utils.AssertUtil.isTrue(AssertUtil.java:37)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl.clockOut(QgzxAttendanceRecordServiceImpl.java:151)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl$$SpringCGLIB$$0.clockOut(<generated>)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController.clockOut(QgzxAttendanceRecordController.java:80)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController$$SpringCGLIB$$0.clockOut(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-07-31 15:23:41.782[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:23:41.795[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:23:41.801[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:23:41.803[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:23:41.806[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-07-31 15:23:41.879[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:23:42.192[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:23:42.201[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:23:42.204[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:23:42.204[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:23:42.205[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xh006(String), xh006(String)
[2m2025-07-31 15:23:42.274[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:23:42.275[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:23:42.282[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:23:42.285[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:23:42.285[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:23:42.285[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 019c96da420aba1d4925a9090357af14(String)
[2m2025-07-31 15:23:42.357[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-31 15:23:43.121[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:23:43.129[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:23:43.132[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:23:43.132[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:23:43.132[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 31f18466f850463284198c847c0ea8f6(String)
[2m2025-07-31 15:23:43.214[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:23:43.290[0;39m [31mERROR[0;39m [35m94895[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.c.handler.GlobalExceptionHandler  [0;39m [2m:[0;39m 无权操作此申请

com.sanythadmin.common.core.exception.BusinessException: 无权操作此申请
	at com.sanythadmin.common.core.utils.AssertUtil.isTrue(AssertUtil.java:37)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl.clockIn(QgzxAttendanceRecordServiceImpl.java:106)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.service.impl.QgzxAttendanceRecordServiceImpl$$SpringCGLIB$$0.clockIn(<generated>)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController.clockIn(QgzxAttendanceRecordController.java:70)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workstudy.controller.QgzxAttendanceRecordController$$SpringCGLIB$$0.clockIn(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-07-31 15:25:32.589[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:25:32.603[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:25:32.606[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:25:32.683[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:25:32.684[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-07-31 15:25:32.761[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:25:33.115[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:25:33.134[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:25:33.144[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:25:33.144[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:25:33.144[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: {{**********}}(String), {{**********}}(String)
[2m2025-07-31 15:25:33.214[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-31 15:25:33.222[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: select 
        a.ID, a.STATUS,a.LOCK_STATUS, a.PASSWORD, a.USERNAME, a.ACTIVE_FLAG,
          a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE,
            a.ACCOUNT_EXPIRE_TIME,a.PASSWORD_EXPIRE_TIME
     ,LISTAGG(d.name, ',') WITHIN GROUP(ORDER BY d.name) roleName
        from syt_sys_account a
        , (select b.account_id, b.role_id, c.name, c.role_scope
        from syt_sys_account_role b, syt_sys_role c
        where b.role_id = c.id) d
        where a.id = d.account_id
         
         
        
         
         
         
        
         
            AND a.ID_CODE LIKE concat(concat('%', ?), '%')
         
         
         
     
        group by
         
        a.ID, a.STATUS,a.LOCK_STATUS, a.PASSWORD, a.USERNAME, a.ACTIVE_FLAG,
          a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE,
            a.ACCOUNT_EXPIRE_TIME,a.PASSWORD_EXPIRE_TIME
[2m2025-07-31 15:25:33.251[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: select 
        a.ID, a.STATUS,a.LOCK_STATUS, a.PASSWORD, a.USERNAME, a.ACTIVE_FLAG,
          a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE,
            a.ACCOUNT_EXPIRE_TIME,a.PASSWORD_EXPIRE_TIME
     ,LISTAGG(d.name, ',') WITHIN GROUP(ORDER BY d.name) roleName
        from syt_sys_account a
        , (select b.account_id, b.role_id, c.name, c.role_scope
        from syt_sys_account_role b, syt_sys_role c
        where b.role_id = c.id) d
        where a.id = d.account_id
         
         
        
         
         
         
        
         
            AND a.ID_CODE LIKE concat(concat('%', ?), '%')
         
         
         
     
        group by
         
        a.ID, a.STATUS,a.LOCK_STATUS, a.PASSWORD, a.USERNAME, a.ACTIVE_FLAG,
          a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE,
            a.ACCOUNT_EXPIRE_TIME,a.PASSWORD_EXPIRE_TIME
[2m2025-07-31 15:25:33.256[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT a.ID, a.STATUS, a.LOCK_STATUS, a.PASSWORD, a.USERNAME, a.ACTIVE_FLAG, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, a.ACCOUNT_EXPIRE_TIME, a.PASSWORD_EXPIRE_TIME, LISTAGG(d.name, ',') WITHIN GROUP (ORDER BY d.name) roleName FROM syt_sys_account a, (SELECT b.account_id, b.role_id, c.name, c.role_scope FROM syt_sys_account_role b, syt_sys_role c WHERE b.role_id = c.id) d WHERE a.id = d.account_id AND a.ID_CODE LIKE concat(concat('%', ?), '%') GROUP BY a.ID, a.STATUS, a.LOCK_STATUS, a.PASSWORD, a.USERNAME, a.ACTIVE_FLAG, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, a.ACCOUNT_EXPIRE_TIME, a.PASSWORD_EXPIRE_TIME
[2m2025-07-31 15:25:33.277[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM (SELECT a.ID, a.STATUS, a.LOCK_STATUS, a.PASSWORD, a.USERNAME, a.ACTIVE_FLAG, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, a.ACCOUNT_EXPIRE_TIME, a.PASSWORD_EXPIRE_TIME, LISTAGG(d.name, ',') WITHIN GROUP (ORDER BY d.name) roleName FROM syt_sys_account a, (SELECT b.account_id, b.role_id, c.name, c.role_scope FROM syt_sys_account_role b, syt_sys_role c WHERE b.role_id = c.id) d WHERE a.id = d.account_id AND a.ID_CODE LIKE concat(concat('%', ?), '%') GROUP BY a.ID, a.STATUS, a.LOCK_STATUS, a.PASSWORD, a.USERNAME, a.ACTIVE_FLAG, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, a.ACCOUNT_EXPIRE_TIME, a.PASSWORD_EXPIRE_TIME) TOTAL
[2m2025-07-31 15:25:33.277[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m ==> Parameters: syL/5SoV7JIAcDw4JtGeXw==(String)
[2m2025-07-31 15:25:33.378[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:25:33.379[0;39m [31mERROR[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.c.s.JwtAuthenticationFilter       [0;39m [2m:[0;39m Username not found

org.springframework.security.core.userdetails.UsernameNotFoundException: Username not found
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.getAccount(JwtAuthenticationFilter.java:121)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:78)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-07-31 15:26:16.459[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:26:16.474[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:26:16.476[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:26:16.477[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:26:16.483[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-07-31 15:26:16.557[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:26:16.885[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:26:16.894[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:26:16.896[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:26:16.897[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:26:16.897[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: **********(String), **********(String)
[2m2025-07-31 15:26:16.978[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:26:16.980[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:26:16.985[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:26:16.987[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:26:16.987[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:26:16.987[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: f8d43934c2a0307c955af06ba196813a(String)
[2m2025-07-31 15:26:17.054[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:26:18.589[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:26:18.596[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:26:18.596[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:26:18.598[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:26:18.598[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 31f18466f850463284198c847c0ea8f6(String)
[2m2025-07-31 15:26:18.668[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:26:18.669[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,EID,XNXQ,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-07-31 15:26:18.679[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,EID,XNXQ,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-07-31 15:26:18.681[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, EID, XNXQ, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-07-31 15:26:18.681[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, EID, XNXQ, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-07-31 15:26:18.682[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-07-31 15:26:18.758[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:26:18.763[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,STUDENT_APPLY_ID,ATTENDANCE_DATE,ATTENDANCE_TYPE,CLOCK_TIME,LOCATION,LONGITUDE,LATITUDE,WORK_DURATION,IS_NORMAL,ATTENDANCE_STATUS,REMARK,CREATE_TIME,UPDATE_TIME    FROM  SYT_QGZX_ATTENDANCE_RECORD         WHERE  (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:18.770[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,STUDENT_APPLY_ID,ATTENDANCE_DATE,ATTENDANCE_TYPE,CLOCK_TIME,LOCATION,LONGITUDE,LATITUDE,WORK_DURATION,IS_NORMAL,ATTENDANCE_STATUS,REMARK,CREATE_TIME,UPDATE_TIME    FROM  SYT_QGZX_ATTENDANCE_RECORD         WHERE  (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:18.772[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, STUDENT_APPLY_ID, ATTENDANCE_DATE, ATTENDANCE_TYPE, CLOCK_TIME, LOCATION, LONGITUDE, LATITUDE, WORK_DURATION, IS_NORMAL, ATTENDANCE_STATUS, REMARK, CREATE_TIME, UPDATE_TIME FROM SYT_QGZX_ATTENDANCE_RECORD WHERE (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:18.772[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, STUDENT_APPLY_ID, ATTENDANCE_DATE, ATTENDANCE_TYPE, CLOCK_TIME, LOCATION, LONGITUDE, LATITUDE, WORK_DURATION, IS_NORMAL, ATTENDANCE_STATUS, REMARK, CREATE_TIME, UPDATE_TIME FROM SYT_QGZX_ATTENDANCE_RECORD WHERE (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:18.773[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m ==> Parameters: 31f18466f850463284198c847c0ea8f6(String), CLOCK_IN(String), 2025-07-31T00:00(LocalDateTime), 2025-07-31T23:59:59(LocalDateTime)
[2m2025-07-31 15:26:18.840[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-31 15:26:18.841[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:26:18.849[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:26:18.851[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:26:18.851[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,EID,XNXQ,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-07-31 15:26:18.864[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,EID,XNXQ,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-07-31 15:26:18.866[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, EID, XNXQ, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-07-31 15:26:18.873[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.insert                      [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO SYT_QGZX_ATTENDANCE_RECORD ( ID, STUDENT_APPLY_ID, ATTENDANCE_DATE, ATTENDANCE_TYPE, CLOCK_TIME, LOCATION, LONGITUDE, LATITUDE, IS_NORMAL, ATTENDANCE_STATUS, CREATE_TIME ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[2m2025-07-31 15:26:18.880[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.insert                      [0;39m [2m:[0;39m ==> Parameters: d4090d3634e1bd436f9aba24520e68e7(String), 31f18466f850463284198c847c0ea8f6(String), 2025-07-31(LocalDate), CLOCK_IN(String), 2025-07-31T15:26:18.588757(LocalDateTime), 东升科技园(String), 64(BigDecimal), 19(BigDecimal), 0(Integer), LATE(String), 2025-07-31T15:26:18.588757(LocalDateTime)
[2m2025-07-31 15:26:18.976[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.w.m.Q.insert                      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-31 15:26:49.984[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:26:50.000[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-07-31 15:26:50.002[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:26:50.005[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-07-31 15:26:50.006[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-07-31 15:26:50.076[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:26:50.382[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:26:50.390[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:26:50.393[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:26:50.393[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-31 15:26:50.394[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: **********(String), **********(String)
[2m2025-07-31 15:26:50.476[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:26:50.477[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:26:50.482[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-31 15:26:50.484[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:26:50.484[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-31 15:26:50.484[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: f8d43934c2a0307c955af06ba196813a(String)
[2m2025-07-31 15:26:50.558[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:26:51.218[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:26:51.225[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:26:51.227[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:26:51.228[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:26:51.229[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 31f18466f850463284198c847c0ea8f6(String)
[2m2025-07-31 15:26:51.320[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:26:51.320[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,EID,XNXQ,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-07-31 15:26:51.332[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,EID,XNXQ,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-07-31 15:26:51.334[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, EID, XNXQ, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-07-31 15:26:51.334[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, EID, XNXQ, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-07-31 15:26:51.334[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-07-31 15:26:51.416[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:26:51.418[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,STUDENT_APPLY_ID,ATTENDANCE_DATE,ATTENDANCE_TYPE,CLOCK_TIME,LOCATION,LONGITUDE,LATITUDE,WORK_DURATION,IS_NORMAL,ATTENDANCE_STATUS,REMARK,CREATE_TIME,UPDATE_TIME    FROM  SYT_QGZX_ATTENDANCE_RECORD         WHERE  (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:51.430[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,STUDENT_APPLY_ID,ATTENDANCE_DATE,ATTENDANCE_TYPE,CLOCK_TIME,LOCATION,LONGITUDE,LATITUDE,WORK_DURATION,IS_NORMAL,ATTENDANCE_STATUS,REMARK,CREATE_TIME,UPDATE_TIME    FROM  SYT_QGZX_ATTENDANCE_RECORD         WHERE  (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:51.433[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, STUDENT_APPLY_ID, ATTENDANCE_DATE, ATTENDANCE_TYPE, CLOCK_TIME, LOCATION, LONGITUDE, LATITUDE, WORK_DURATION, IS_NORMAL, ATTENDANCE_STATUS, REMARK, CREATE_TIME, UPDATE_TIME FROM SYT_QGZX_ATTENDANCE_RECORD WHERE (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:51.434[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, STUDENT_APPLY_ID, ATTENDANCE_DATE, ATTENDANCE_TYPE, CLOCK_TIME, LOCATION, LONGITUDE, LATITUDE, WORK_DURATION, IS_NORMAL, ATTENDANCE_STATUS, REMARK, CREATE_TIME, UPDATE_TIME FROM SYT_QGZX_ATTENDANCE_RECORD WHERE (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:51.436[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m ==> Parameters: 31f18466f850463284198c847c0ea8f6(String), CLOCK_IN(String), 2025-07-31T00:00(LocalDateTime), 2025-07-31T23:59:59(LocalDateTime)
[2m2025-07-31 15:26:51.507[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 15:26:51.514[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,STUDENT_APPLY_ID,ATTENDANCE_DATE,ATTENDANCE_TYPE,CLOCK_TIME,LOCATION,LONGITUDE,LATITUDE,WORK_DURATION,IS_NORMAL,ATTENDANCE_STATUS,REMARK,CREATE_TIME,UPDATE_TIME    FROM  SYT_QGZX_ATTENDANCE_RECORD         WHERE  (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:51.521[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,STUDENT_APPLY_ID,ATTENDANCE_DATE,ATTENDANCE_TYPE,CLOCK_TIME,LOCATION,LONGITUDE,LATITUDE,WORK_DURATION,IS_NORMAL,ATTENDANCE_STATUS,REMARK,CREATE_TIME,UPDATE_TIME    FROM  SYT_QGZX_ATTENDANCE_RECORD         WHERE  (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:51.524[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, STUDENT_APPLY_ID, ATTENDANCE_DATE, ATTENDANCE_TYPE, CLOCK_TIME, LOCATION, LONGITUDE, LATITUDE, WORK_DURATION, IS_NORMAL, ATTENDANCE_STATUS, REMARK, CREATE_TIME, UPDATE_TIME FROM SYT_QGZX_ATTENDANCE_RECORD WHERE (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:51.524[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, STUDENT_APPLY_ID, ATTENDANCE_DATE, ATTENDANCE_TYPE, CLOCK_TIME, LOCATION, LONGITUDE, LATITUDE, WORK_DURATION, IS_NORMAL, ATTENDANCE_STATUS, REMARK, CREATE_TIME, UPDATE_TIME FROM SYT_QGZX_ATTENDANCE_RECORD WHERE (STUDENT_APPLY_ID = ? AND ATTENDANCE_TYPE = ? AND ATTENDANCE_DATE >= ? AND ATTENDANCE_DATE <= ?)
[2m2025-07-31 15:26:51.524[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m ==> Parameters: 31f18466f850463284198c847c0ea8f6(String), CLOCK_OUT(String), 2025-07-31T00:00(LocalDateTime), 2025-07-31T23:59:59(LocalDateTime)
[2m2025-07-31 15:26:51.619[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-31 15:26:51.620[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:26:51.628[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID=?
[2m2025-07-31 15:26:51.630[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE ID = ?
[2m2025-07-31 15:26:51.631[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,EID,XNXQ,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-07-31 15:26:51.641[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,EID,XNXQ,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-07-31 15:26:51.643[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.c.c.h.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, EID, XNXQ, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-07-31 15:26:51.644[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.insert                      [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO SYT_QGZX_ATTENDANCE_RECORD ( ID, STUDENT_APPLY_ID, ATTENDANCE_DATE, ATTENDANCE_TYPE, CLOCK_TIME, LOCATION, LONGITUDE, LATITUDE, WORK_DURATION, IS_NORMAL, ATTENDANCE_STATUS, CREATE_TIME ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[2m2025-07-31 15:26:51.646[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.insert                      [0;39m [2m:[0;39m ==> Parameters: 643a1239a58efe0437cd489bdb3e22f4(String), 31f18466f850463284198c847c0ea8f6(String), 2025-07-31(LocalDate), CLOCK_OUT(String), 2025-07-31T15:26:51.218031(LocalDateTime), 东升科技园(String), 94(BigDecimal), 64(BigDecimal), 0.00(BigDecimal), 0(Integer), EARLY_LEAVE(String), 2025-07-31T15:26:51.218031(LocalDateTime)
[2m2025-07-31 15:26:51.757[0;39m [32mDEBUG[0;39m [35m94895[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.insert                      [0;39m [2m:[0;39m <==    Updates: 1
